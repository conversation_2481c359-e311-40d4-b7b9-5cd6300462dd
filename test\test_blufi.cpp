#include <Arduino.h>
#include <unity.h>
#include "../src/ConnectivityManager.h"

// Test ConnectivityManager initialization
void test_connectivity_manager_init() {
    ConnectivityManager connectivity;
    
    // Test initialization
    bool result = connectivity.begin("test.mqtt.server", 1883, "testuser", "testpass");
    TEST_ASSERT_TRUE(result);
    
    // Test initial state
    TEST_ASSERT_FALSE(connectivity.isConnected());
    TEST_ASSERT_EQUAL(0, connectivity.getWiFiFailureCount());
}

// Test WiFi failure count management
void test_wifi_failure_count() {
    ConnectivityManager connectivity;
    connectivity.begin("test.mqtt.server", 1883);
    
    // Test initial count
    TEST_ASSERT_EQUAL(0, connectivity.getWiFiFailureCount());
    
    // Test increment
    connectivity.incrementWiFiFailureCount();
    TEST_ASSERT_EQUAL(1, connectivity.getWiFiFailureCount());
    
    connectivity.incrementWiFiFailureCount();
    TEST_ASSERT_EQUAL(2, connectivity.getWiFiFailureCount());
    
    // Test reset
    connectivity.resetWiFiFailureCount();
    TEST_ASSERT_EQUAL(0, connectivity.getWiFiFailureCount());
}

// Test credentials management
void test_credentials_management() {
    ConnectivityManager connectivity;
    connectivity.begin("test.mqtt.server", 1883);
    
    // Initially should have no stored credentials (in test environment)
    // Note: This test may vary depending on the test environment
    // TEST_ASSERT_FALSE(connectivity.hasStoredCredentials());
    
    // Test clearing credentials
    connectivity.clearStoredCredentials();
    TEST_ASSERT_EQUAL(0, connectivity.getWiFiFailureCount());
}

// Test connection status string
void test_connection_status() {
    ConnectivityManager connectivity;
    connectivity.begin("test.mqtt.server", 1883);
    
    String status = connectivity.getConnectionStatus();
    TEST_ASSERT_TRUE(status.length() > 0);
    TEST_ASSERT_TRUE(status.indexOf("WiFi:") >= 0);
    TEST_ASSERT_TRUE(status.indexOf("MQTT:") >= 0);
    TEST_ASSERT_TRUE(status.indexOf("Failures:") >= 0);
}

void setup() {
    Serial.begin(115200);
    delay(2000); // Wait for serial to initialize
    
    UNITY_BEGIN();
    
    RUN_TEST(test_connectivity_manager_init);
    RUN_TEST(test_wifi_failure_count);
    RUN_TEST(test_credentials_management);
    RUN_TEST(test_connection_status);
    
    UNITY_END();
}

void loop() {
    // Empty loop for unit tests
}
