/*
 * BluFi WiFi Provisioning Example for Fish Tank Sensor
 * 
 * This example demonstrates how to use the BluFi provisioning feature
 * to configure WiFi credentials without hardcoding them in the firmware.
 * 
 * Features:
 * - Automatic BluFi provisioning on first boot
 * - WiFi failure tracking and permanent sleep protection
 * - Stored credentials management
 * 
 * Usage:
 * 1. Upload this code to your ESP32
 * 2. Install ESP BLE Provisioning app on your phone
 * 3. Follow the provisioning process as described in BluFi_Setup_Guide.md
 */

#include <Arduino.h>
#include "ConnectivityManager.h"

// Create connectivity manager instance
ConnectivityManager connectivity;

// System state
bool systemReady = false;

void setup() {
    Serial.begin(115200);
    delay(2000); // Wait for serial to initialize
    
    Serial.println("\n=== BluFi Provisioning Example ===");
    Serial.println("Fish Tank Sensor - WiFi Configuration Demo");
    
    // Initialize connectivity manager
    if (!connectivity.begin("test.mqtt.server", 1883, "testuser", "testpass")) {
        Serial.println("Failed to initialize connectivity manager!");
        return;
    }
    
    Serial.println("Connectivity manager initialized");
    
    // Check WiFi connection status
    if (!handleWiFiConnection()) {
        Serial.println("WiFi connection failed!");
        
        // Check if we've exceeded maximum failures
        if (connectivity.getWiFiFailureCount() >= 3) {
            Serial.println("Maximum WiFi failures reached!");
            Serial.println("Entering permanent deep sleep...");
            enterPermanentDeepSleep();
            return;
        }
        
        Serial.println("Will retry on next boot");
        enterDeepSleep(30); // Sleep for 30 seconds and retry
        return;
    }
    
    Serial.println("WiFi connection successful!");
    systemReady = true;
    
    // Print connection status
    Serial.println(connectivity.getConnectionStatus());
    
    // Try to connect to MQTT
    if (connectivity.connectMQTT(10000)) {
        Serial.println("MQTT connection successful!");
    } else {
        Serial.println("MQTT connection failed, but WiFi is working");
    }
}

void loop() {
    if (!systemReady) {
        delay(1000);
        return;
    }
    
    // Main application loop
    Serial.println("System running normally...");
    Serial.println(connectivity.getConnectionStatus());
    
    // Handle MQTT loop if connected
    if (connectivity.isConnected()) {
        connectivity.loop();
        
        // Example: Publish test data every 30 seconds
        static unsigned long lastPublish = 0;
        if (millis() - lastPublish > 30000) {
            // Note: This is just an example - you would use real sensor data
            // connectivity.publishStatus(1, "test_status");
            lastPublish = millis();
            Serial.println("Would publish sensor data here...");
        }
    }
    
    delay(5000); // Wait 5 seconds before next iteration
}

bool handleWiFiConnection() {
    Serial.println("\n--- WiFi Connection Process ---");
    
    // Check if we have stored WiFi credentials
    if (!connectivity.hasStoredCredentials()) {
        Serial.println("No stored WiFi credentials found");
        Serial.println("Starting BluFi provisioning...");
        
        // Start BluFi provisioning (3 minutes timeout)
        if (!connectivity.startBluFiProvisioning(180000)) {
            Serial.println("BluFi provisioning failed or timed out");
            return false;
        }
        
        Serial.println("BluFi provisioning completed successfully");
        return true;
    } else {
        Serial.println("Found stored WiFi credentials, attempting connection...");
        
        // Try to connect using stored credentials
        if (connectivity.connectWiFi(30000)) {
            Serial.println("WiFi connection successful");
            return true;
        } else {
            Serial.println("WiFi connection failed with stored credentials");
            return false;
        }
    }
}

void enterPermanentDeepSleep() {
    Serial.println("\n=== ENTERING PERMANENT DEEP SLEEP ===");
    Serial.println("Device will not wake up automatically");
    Serial.println("Reset the device to restart");
    Serial.flush();
    
    // Disconnect everything
    connectivity.disconnect();
    
    // Clear any wake-up sources
    esp_sleep_disable_wakeup_source(ESP_SLEEP_WAKEUP_ALL);
    
    // Enter deep sleep without any wake-up timer
    esp_deep_sleep_start();
}

void enterDeepSleep(uint32_t seconds) {
    Serial.printf("Entering deep sleep for %u seconds...\n", seconds);
    Serial.flush();
    
    // Disconnect to save power
    connectivity.disconnect();
    
    // Configure timer wake-up
    esp_sleep_enable_timer_wakeup(seconds * 1000000ULL);
    
    // Enter deep sleep
    esp_deep_sleep_start();
}

// Utility function to clear stored credentials (for testing)
void clearStoredCredentials() {
    Serial.println("Clearing stored WiFi credentials...");
    connectivity.clearStoredCredentials();
    Serial.println("Credentials cleared. Device will enter provisioning mode on next boot.");
}

// Utility function to print system information
void printSystemInfo() {
    Serial.println("\n=== System Information ===");
    Serial.printf("Chip model: %s\n", ESP.getChipModel());
    Serial.printf("Chip revision: %d\n", ESP.getChipRevision());
    Serial.printf("CPU frequency: %d MHz\n", ESP.getCpuFreqMHz());
    Serial.printf("Flash size: %d bytes\n", ESP.getFlashChipSize());
    Serial.printf("Free heap: %u bytes\n", ESP.getFreeHeap());
    Serial.printf("WiFi failure count: %d\n", connectivity.getWiFiFailureCount());
    Serial.println("==========================");
}
