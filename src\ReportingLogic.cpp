#include "ReportingLogic.h"

ReportingLogic::ReportingLogic(SensorDataManager& dataManager, ConnectivityManager& connectivity) :
    dataManager(dataManager),
    connectivity(connectivity),
    initialized(false),
    totalReports(0),
    thresholdReports(0),
    dailyReports(0),
    errorReports(0) {
}

void ReportingLogic::begin() {
    initialized = true;
    Serial.println("ReportingLogic initialized");
}

ReportDecision ReportingLogic::analyzeReading(const SensorReading& currentReading) {
    ReportDecision decision;
    
    if (!currentReading.valid) {
        decision.shouldReport = true;
        decision.reasons |= REPORT_ERROR;
        decision.reasonText = "Invalid sensor reading";
        return decision;
    }
    
    // Check if this is the first reading
    SensorReading lastReading = dataManager.getLastReading();
    if (!lastReading.valid) {
        decision.shouldReport = true;
        decision.reasons |= REPORT_FIRST_READING;
    }
    
    // Check threshold-based reporting
    if (dataManager.shouldReportByThreshold(currentReading)) {
        decision.shouldReport = true;
        
        if (lastReading.valid) {
            int tdsDiff = abs(currentReading.tds - lastReading.tds);
            float tempDiff = abs(currentReading.temperature - lastReading.temperature);
            
            if (tdsDiff >= 1000) {
                decision.reasons |= REPORT_TDS_THRESHOLD;
            }
            if (tempDiff >= 1.0) {
                decision.reasons |= REPORT_TEMP_THRESHOLD;
            }
        }
    }
    
    // Check daily reporting
    if (dataManager.shouldReportDaily()) {
        decision.shouldReport = true;
        decision.reasons |= REPORT_DAILY;
    }
    
    // Build reason text
    decision.reasonText = buildReasonText(decision.reasons);
    
    return decision;
}

bool ReportingLogic::executeReport(const SensorReading& reading, const ReportDecision& decision) {
    Serial.printf("Executing report: %s\n", decision.reasonText.c_str());
    
    // Attempt to connect if not already connected
    if (!attemptConnection()) {
        Serial.println("Failed to establish connection for reporting");
        logReportAttempt(decision, false);
        return false;
    }
    
    // Determine if this is a daily report
    bool isDailyReport = (decision.reasons & REPORT_DAILY) != 0;
    
    // Publish sensor data
    bool success = connectivity.publishSensorData(reading, isDailyReport);
    
    if (success) {
        // Update reporting state
        dataManager.updateReportState(isDailyReport);
        
        // Update statistics
        totalReports++;
        if (decision.reasons & REPORT_DAILY) dailyReports++;
        if (decision.reasons & (REPORT_TDS_THRESHOLD | REPORT_TEMP_THRESHOLD)) thresholdReports++;
        if (decision.reasons & REPORT_ERROR) errorReports++;
        
        Serial.printf("Report successful! Total reports: %u\n", totalReports);
    }
    
    logReportAttempt(decision, success);
    return success;
}

bool ReportingLogic::isDailyReportDue() {
    return dataManager.shouldReportDaily();
}

bool ReportingLogic::forceReport(const SensorReading& reading, const char* reason) {
    ReportDecision decision;
    decision.shouldReport = true;
    decision.reasons = REPORT_NONE; // Custom reason
    decision.reasonText = String("Forced: ") + reason;
    
    return executeReport(reading, decision);
}

void ReportingLogic::printReportingStats() {
    Serial.println("=== Reporting Statistics ===");
    Serial.printf("Total reports: %u\n", totalReports);
    Serial.printf("Threshold reports: %u\n", thresholdReports);
    Serial.printf("Daily reports: %u\n", dailyReports);
    Serial.printf("Error reports: %u\n", errorReports);
    
    ReportingState state = dataManager.getReportingState();
    Serial.printf("Boot count: %u\n", state.bootCount);
    Serial.printf("Last report time: %lu\n", state.lastReportTime);
    Serial.printf("Next daily report: %lu\n", state.nextDailyReportTime);
    Serial.println("============================");
}

String ReportingLogic::buildReasonText(uint8_t reasons) {
    if (reasons == REPORT_NONE) {
        return "No report needed";
    }
    
    String text = "";
    bool first = true;
    
    if (reasons & REPORT_FIRST_READING) {
        if (!first) text += ", ";
        text += "First reading";
        first = false;
    }
    
    if (reasons & REPORT_TDS_THRESHOLD) {
        if (!first) text += ", ";
        text += "TDS threshold exceeded";
        first = false;
    }
    
    if (reasons & REPORT_TEMP_THRESHOLD) {
        if (!first) text += ", ";
        text += "Temperature threshold exceeded";
        first = false;
    }
    
    if (reasons & REPORT_DAILY) {
        if (!first) text += ", ";
        text += "Daily report due";
        first = false;
    }
    
    if (reasons & REPORT_ERROR) {
        if (!first) text += ", ";
        text += "Error condition";
        first = false;
    }
    
    return text;
}

bool ReportingLogic::attemptConnection() {
    // Check if already connected
    if (connectivity.isConnected()) {
        return true;
    }
    
    Serial.println("Attempting to establish connection...");
    
    // Try to connect to WiFi
    if (!connectivity.connectWiFi(WIFI_TIMEOUT_MS)) {
        Serial.println("WiFi connection failed");
        return false;
    }

    // Try to connect to MQTT
    if (!connectivity.connectMQTT(MQTT_TIMEOUT_MS)) {
        Serial.println("MQTT connection failed");
        return false;
    }
    
    Serial.println("Connection established successfully");
    return true;
}

void ReportingLogic::logReportAttempt(const ReportDecision& decision, bool success) {
    Serial.printf("Report attempt - Reason: %s, Success: %s\n", 
                  decision.reasonText.c_str(), 
                  success ? "YES" : "NO");
    
    if (!success) {
        Serial.printf("Connection status: %s\n", connectivity.getConnectionStatus().c_str());
    }
}
